-- Project configuration
set_project("rrjson")
set_version("1.0.0")
set_languages("c++23")

-- Build modes
add_rules("mode.debug", "mode.release")

-- Set compiler flags
set_warnings("all", "error")
if is_mode("debug") then
    set_symbols("debug")
    set_optimize("none")
elseif is_mode("release") then
    set_symbols("hidden")
    set_optimize("fastest")
    set_strip("all")
end

-- Header-only library target
target("rrjson")
    set_kind("headeronly")
    add_headerfiles("lib/**.hpp")
    add_includedirs("lib", {public = true})

-- Example target
target("example")
    set_kind("binary")
    add_deps("rrjson")
    add_files("examples/*.cpp")
    add_includedirs("lib")

-- Test target
target("test")
    set_kind("binary")
    add_deps("rrjson")
    add_files("tests/*.cpp")
    add_includedirs("lib")
    set_default(false)

-- Installation
target("rrjson")
    on_install(function (target)
        local headerdir = path.join(target:installdir(), "include", "rrjson")
        os.cp("lib/*.hpp", headerdir)
    end)