#include "rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    // JSON data as string (will be moved into parser)
    std::string json_data = R"({
        "name": "<PERSON>",
        "age": 30,
        "city": "New York",
        "hobbies": ["reading", "swimming", "coding"],
        "address": {
            "street": "123 Main St",
            "zipcode": "10001"
        }
    })";

    std::cout << "rrjson Basic Usage Example\n";
    std::cout << "==========================\n\n";

    std::cout << "JSON Data:\n" << json_data << "\n\n";

    try {
        // Move JSON data into parser to prevent user modification
        rrjson::Parser parser(std::move(json_data));

        // Note: json_data is now empty/moved, user cannot modify it
        std::cout << "JSON data after move: '" << json_data << "' (empty)\n\n";

        auto root = parser.parse();

        std::cout << "Parser data size: " << parser.data_size() << " bytes\n";
        std::cout << "Can resume: " << (parser.can_resume() ? "yes" : "no") << "\n\n";

        // TODO: Implement actual parsing when rrjson.hpp is complete
        std::cout << "Parsing functionality will be implemented in rrjson.hpp\n";

        // Example of how the API will work:
        // if (root) {
        //     std::cout << "Name: " << root["name"].as_string() << std::endl;
        //     std::cout << "Age: " << root["age"].as_int() << std::endl;
        // }

        // Demonstrate type error handling with detailed messages
        std::cout << "Demonstrating type error handling:\n";

        // Create test elements to show error messages
        rrjson::Element bool_element("test", true);
        rrjson::Element number_element("test", 42.0);

        try {
            // This will work fine
            std::cout << "✓ Bool element as bool: " << bool_element.as_bool() << "\n";

            // This will throw a detailed type_error
            std::cout << "Trying to access bool as string...\n";
            bool_element.as_string();

        } catch (const rrjson::type_error& e) {
            std::cout << "✗ Caught type error: " << e.what() << "\n";
            std::cout << "  - Actual type was: " <<
                (e.actual_type() == rrjson::ElementType::Bool ? "Bool" : "Other") << "\n";
            std::cout << "  - Requested type was: " << e.requested_type() << "\n";
        }

        try {
            // This will also throw a detailed type_error
            std::cout << "Trying to access number as bool...\n";
            number_element.as_bool();

        } catch (const rrjson::type_error& e) {
            std::cout << "✗ Caught type error: " << e.what() << "\n";
            std::cout << "  - Actual type was: " <<
                (e.actual_type() == rrjson::ElementType::Number ? "Number" : "Other") << "\n";
            std::cout << "  - Requested type was: " << e.requested_type() << "\n";
        }

        // Demonstrate index and key access errors
        std::cout << "\nDemonstrating index and key access errors:\n";

        // Create test array
        rrjson::Element::ArrayType test_array = {
            rrjson::Element("0", 1.0),
            rrjson::Element("1", 2.0)
        };
        rrjson::Element array_element("test_array", test_array);

        try {
            std::cout << "Trying to access array[5] (out of bounds)...\n";
            auto elem = array_element[5];
        } catch (const rrjson::index_error& e) {
            std::cout << "✗ Caught index error: " << e.what() << "\n";
            std::cout << "  - Index: " << e.index() << ", Size: " << e.size() << "\n";
        }

        try {
            std::cout << "Trying to access string as array...\n";
            auto elem = bool_element[0];  // Bool is not an array
        } catch (const rrjson::type_error& e) {
            std::cout << "✗ Caught type error: " << e.what() << "\n";
        }

        // Create test object
        rrjson::Element::ObjectType test_object;
        test_object["name"] = rrjson::Element("name", std::string_view("John"));
        rrjson::Element object_element("test_object", test_object);

        try {
            std::cout << "Trying to access object['nonexistent'] (key not found)...\n";
            auto elem = object_element["nonexistent"];
        } catch (const rrjson::key_error& e) {
            std::cout << "✗ Caught key error: " << e.what() << "\n";
            std::cout << "  - Key: '" << e.key() << "'\n";
        }

        try {
            std::cout << "Trying to get size() of a number...\n";
            auto s = number_element.size();  // Number doesn't have size
            (void)s;
        } catch (const rrjson::type_error& e) {
            std::cout << "✗ Caught type error: " << e.what() << "\n";
        }

    } catch (const rrjson::type_error& e) {
        std::cerr << "Type error: " << e.what() << std::endl;
        std::cerr << "Actual type: " << (int)e.actual_type() << ", Requested: " << e.requested_type() << std::endl;
    } catch (const rrjson::index_error& e) {
        std::cerr << "Index error: " << e.what() << std::endl;
    } catch (const rrjson::key_error& e) {
        std::cerr << "Key error: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }

    return 0;
}
