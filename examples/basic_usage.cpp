#include "rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    // JSON data as string (will be moved into parser)
    std::string json_data = R"({
        "name": "<PERSON>",
        "age": 30,
        "city": "New York",
        "hobbies": ["reading", "swimming", "coding"],
        "address": {
            "street": "123 Main St",
            "zipcode": "10001"
        }
    })";

    std::cout << "rrjson Basic Usage Example\n";
    std::cout << "==========================\n\n";

    std::cout << "JSON Data:\n" << json_data << "\n\n";

    try {
        // Move JSON data into parser to prevent user modification
        rrjson::Parser parser(std::move(json_data));

        // Note: json_data is now empty/moved, user cannot modify it
        std::cout << "JSON data after move: '" << json_data << "' (empty)\n\n";

        auto root = parser.parse();

        std::cout << "Parser data size: " << parser.data_size() << " bytes\n";
        std::cout << "Can resume: " << (parser.can_resume() ? "yes" : "no") << "\n\n";

        // TODO: Implement actual parsing when rrjson.hpp is complete
        std::cout << "Parsing functionality will be implemented in rrjson.hpp\n";

        // Example of how the API will work:
        // if (root) {
        //     std::cout << "Name: " << root["name"].as_string() << std::endl;
        //     std::cout << "Age: " << root["age"].as_int() << std::endl;
        //
        //     // This would throw type_error if "age" is not a string:
        //     // std::cout << "Age as string: " << root["age"].as_string() << std::endl;
        // }

    } catch (const rrjson::type_error& e) {
        std::cerr << "Type error: " << e.what() << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }

    return 0;
}
