#include "rrjson.hpp"
#include <iostream>

int main() {
    const char* json_data = R"({
        "name": "<PERSON>",
        "age": 30,
        "city": "New York",
        "hobbies": ["reading", "swimming", "coding"],
        "address": {
            "street": "123 Main St",
            "zipcode": "10001"
        }
    })";
    
    std::cout << "rrjson Basic Usage Example\n";
    std::cout << "==========================\n\n";
    
    std::cout << "JSON Data:\n" << json_data << "\n\n";
    
    // TODO: Implement actual parsing when rrjson.hpp is complete
    std::cout << "Parsing functionality will be implemented in rrjson.hpp\n";
    
    return 0;
}
