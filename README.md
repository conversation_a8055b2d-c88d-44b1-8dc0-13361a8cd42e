# rrjson

一个高性能、可恢复的只读 JSON 解析器，专为内存敏感和流式处理场景设计。

## 核心特性

- **Resumability (可恢复性)**: 支持中断和恢复解析过程，适用于大文件和流式处理
- **Read-only (只读)**: 零拷贝设计，直接引用原始数据，避免不必要的内存分配
- **数据保护**: 使用移动语义和 Helper 类防止用户修改原始 JSON 数据
- **类型安全**: `as_xxx` 方法在类型不匹配时抛出明确的错误信息
- **内存占用最小化**: Element 中全部使用 `std::string_view`，避免字符串拷贝
- **现代 C++**: 使用后置返回类型、移动语义等 C++17 特性
- **Header-only**: 单头文件库，易于集成

## 快速开始

### 安装

#### 使用 xmake

```bash
# 克隆仓库
git clone https://github.com/your-username/rrjson.git
cd rrjson

# 构建项目
xmake

# 运行示例
xmake run example

# 运行测试
xmake run test
```

#### 手动集成

由于 rrjson 是 header-only 库，您只需要将 `lib/rrjson.hpp` 复制到您的项目中：

```cpp
#include "rrjson.hpp"
```

### 基本用法

```cpp
#include "rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    // JSON 数据将被移动到 Parser 中，防止用户修改
    std::string json_data = R"({
        "name": "John Doe",
        "age": 30,
        "city": "New York"
    })";

    try {
        // 使用 move 语义，JSON 数据被安全地转移到 Parser
        rrjson::Parser parser(std::move(json_data));

        // json_data 现在为空，用户无法修改原始数据
        auto root = parser.parse();

        if (root) {
            // 类型安全的访问，类型不匹配时会抛出异常
            std::cout << "Name: " << root["name"].as_string() << std::endl;
            std::cout << "Age: " << root["age"].as_int() << std::endl;
        }

    } catch (const rrjson::type_error& e) {
        std::cerr << "类型错误: " << e.what() << std::endl;
    }

    return 0;
}
```

## API 文档

### 核心类

#### `rrjson::Parser`

JSON 解析器主类，使用移动语义保护 JSON 数据不被修改。

```cpp
class Parser {
public:
    // 构造函数接受 JSON 数据的所有权
    explicit Parser(std::string&& json_raw);

    // 禁用拷贝，允许移动
    Parser(const Parser&) = delete;
    Parser(Parser&&) = default;

    // 使用后置返回类型的方法
    auto parse() -> Element;
    auto can_resume() const -> bool;
    auto resume() -> void;
    auto current_position() const -> size_t;
    auto data_size() const -> size_t;
};
```

#### `rrjson::Element`

表示 JSON 中的一个元素，全部使用 `string_view` 避免字符串拷贝。

```cpp
class Element {
public:
    enum class ElementType { Null, Bool, Number, String, Array, Object };

    // 类型检查（后置返回类型）
    auto type() const -> ElementType;
    auto is_null() const -> bool;
    auto is_bool() const -> bool;
    auto is_number() const -> bool;
    auto is_string() const -> bool;
    auto is_array() const -> bool;
    auto is_object() const -> bool;

    // 类型安全的值访问（类型不匹配时抛出 type_error）
    auto as_bool() const -> bool;
    auto as_number() const -> double;
    auto as_int() const -> int;
    auto as_string() const -> std::string_view;

    // 容器访问
    auto operator[](std::string_view key) const -> Element;  // 对象访问
    auto operator[](size_t index) const -> Element;          // 数组访问
    auto size() const -> size_t;                             // 数组/对象大小

    // 恢复功能
    auto resume_index() const -> size_t;
    auto set_resume_index(size_t idx) -> void;
    auto name() const -> std::string_view;
};
```

#### `rrjson::type_error`

类型转换失败时抛出的异常，包含详细的类型信息。

```cpp
class type_error : public std::runtime_error {
public:
    // 构造函数接受实际类型和请求类型，自动构建错误消息
    type_error(ElementType actual_type, const std::string& requested_type);

    // 获取实际类型和请求类型的信息
    auto actual_type() const -> ElementType;
    auto requested_type() const -> const std::string&;
};
```

**错误消息格式**: `"Element is not a [requested_type] type (actual type: [actual_type])"`

**示例**:
```cpp
try {
    element.as_string();  // 如果 element 是 number 类型
} catch (const rrjson::type_error& e) {
    // e.what() 返回: "Element is not a string type (actual type: number)"
    // e.actual_type() 返回: ElementType::Number
    // e.requested_type() 返回: "string"
}
```

## 语法结构

本库完全遵循 [JSON 标准](https://www.json.org/)，支持：

- **对象**: `{"key": "value"}`
- **数组**: `[1, 2, 3]`
- **字符串**: `"hello world"`
- **数字**: `123`, `123.45`, `1.23e-4`
- **布尔值**: `true`, `false`
- **空值**: `null`

## 数据结构设计

### Element 结构

每个 `Element` 包含：

- **name**: 元素名称（对于对象成员）
- **value**: 元素值的引用
- **resume_index**: 恢复解析的位置索引
  - 指向 `value[xxx]`（仅当 `typeof(Element) == Object | Array` 时有效）
  - 否则为 `nullptr`

### 内存布局

```
JSON Text: {"name": "John", "age": 30}
           ^      ^      ^     ^
           |      |      |     |
Elements:  |      |      |     +-- Element{name: "age", value: "30"}
           |      |      +-------- Element{name: "", value: "John"}
           |      +--------------- Element{name: "name", value: "John"}
           +---------------------- Element{name: "", value: entire_object}
```

## 实现原理

1. **词法分析**: 通过 `lexer` 将 JSON 文本分解为 token
2. **零拷贝解析**: 使用 `std::string_view` 记录 `name` 和 `value`，避免字符串复制
3. **递归下降**: 递归解析嵌套的对象和数组结构
4. **恢复机制**: 记录解析位置，支持中断后恢复解析

## 构建要求

- C++17 或更高版本
- 支持的编译器：
  - GCC 7.0+
  - Clang 5.0+
  - MSVC 2017+

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [JSON 官方规范](https://www.json.org/)
- 感谢所有贡献者的支持