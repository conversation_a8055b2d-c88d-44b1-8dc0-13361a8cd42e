# rrjson

一个高性能、可恢复的只读 JSON 解析器，专为内存敏感和流式处理场景设计。

## 核心特性

- **Resumability (可恢复性)**: 支持中断和恢复解析过程，适用于大文件和流式处理
- **Read-only (只读)**: 零拷贝设计，直接引用原始数据，避免不必要的内存分配
- **内存占用最小化**: 基于 `std::string_view` 和 `std::span`，最小化内存占用
- **Header-only**: 单头文件库，易于集成
- **C++17**: 使用现代 C++ 特性，保证性能和可读性

## 快速开始

### 安装

#### 使用 xmake

```bash
# 克隆仓库
git clone https://github.com/your-username/rrjson.git
cd rrjson

# 构建项目
xmake

# 运行示例
xmake run example

# 运行测试
xmake run test
```

#### 手动集成

由于 rrjson 是 header-only 库，您只需要将 `lib/rrjson.hpp` 复制到您的项目中：

```cpp
#include "rrjson.hpp"
```

### 基本用法

```cpp
#include "rrjson.hpp"
#include <iostream>

int main() {
    const char* json_data = R"({
        "name": "John Doe",
        "age": 30,
        "city": "New York"
    })";

    rrjson::Parser parser(json_data);
    auto root = parser.parse();

    if (root) {
        std::cout << "Name: " << root["name"].as_string() << std::endl;
        std::cout << "Age: " << root["age"].as_int() << std::endl;
    }

    return 0;
}
```

## API 文档

### 核心类

#### `rrjson::Parser`

JSON 解析器主类，负责将 JSON 文本解析为元素树。

```cpp
class Parser {
public:
    explicit Parser(std::string_view json_text);
    Element parse();
    bool can_resume() const;
    void resume();
};
```

#### `rrjson::Element`

表示 JSON 中的一个元素（对象、数组、字符串、数字等）。

```cpp
class Element {
public:
    enum class Type { Null, Bool, Number, String, Array, Object };

    Type type() const;
    bool is_null() const;
    bool is_bool() const;
    bool is_number() const;
    bool is_string() const;
    bool is_array() const;
    bool is_object() const;

    bool as_bool() const;
    double as_number() const;
    int as_int() const;
    std::string_view as_string() const;

    Element operator[](std::string_view key) const;  // 对象访问
    Element operator[](size_t index) const;          // 数组访问

    size_t size() const;  // 数组/对象大小
};
```

## 语法结构

本库完全遵循 [JSON 标准](https://www.json.org/)，支持：

- **对象**: `{"key": "value"}`
- **数组**: `[1, 2, 3]`
- **字符串**: `"hello world"`
- **数字**: `123`, `123.45`, `1.23e-4`
- **布尔值**: `true`, `false`
- **空值**: `null`

## 数据结构设计

### Element 结构

每个 `Element` 包含：

- **name**: 元素名称（对于对象成员）
- **value**: 元素值的引用
- **resume_index**: 恢复解析的位置索引
  - 指向 `value[xxx]`（仅当 `typeof(Element) == Object | Array` 时有效）
  - 否则为 `nullptr`

### 内存布局

```
JSON Text: {"name": "John", "age": 30}
           ^      ^      ^     ^
           |      |      |     |
Elements:  |      |      |     +-- Element{name: "age", value: "30"}
           |      |      +-------- Element{name: "", value: "John"}
           |      +--------------- Element{name: "name", value: "John"}
           +---------------------- Element{name: "", value: entire_object}
```

## 实现原理

1. **词法分析**: 通过 `lexer` 将 JSON 文本分解为 token
2. **零拷贝解析**: 使用 `std::string_view` 记录 `name` 和 `value`，避免字符串复制
3. **递归下降**: 递归解析嵌套的对象和数组结构
4. **恢复机制**: 记录解析位置，支持中断后恢复解析

## 构建要求

- C++17 或更高版本
- 支持的编译器：
  - GCC 7.0+
  - Clang 5.0+
  - MSVC 2017+

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 开启 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [JSON 官方规范](https://www.json.org/)
- 感谢所有贡献者的支持