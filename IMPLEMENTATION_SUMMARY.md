# rrjson 实现总结

## 已实现的功能

### 1. 数据保护机制

#### JsonDataHelper 类
- 使用移动语义接管 JSON 原始数据的所有权
- 禁用拷贝构造和拷贝赋值，防止数据被意外复制
- 提供只读的 `std::string_view` 接口访问数据
- 确保用户无法修改原始 JSON 数据

```cpp
// 用户数据被安全地移动到 Parser 中
std::string json_data = "...";
rrjson::Parser parser(std::move(json_data));
// json_data 现在为空，用户无法修改原始数据
```

### 2. 现代 C++ 特性

#### 后置返回类型
所有方法都使用后置返回类型语法：

```cpp
auto type() const -> ElementType;
auto as_bool() const -> bool;
auto as_string() const -> std::string_view;
auto operator[](std::string_view key) const -> Element;
```

#### 移动语义
- Parser 构造函数使用 `std::string&&` 参数
- 禁用拷贝，允许移动操作
- 使用 `std::unique_ptr` 管理 JsonDataHelper

### 3. 类型安全

#### 严格的类型检查
`as_xxx` 方法在类型不匹配时抛出详细的 `type_error` 异常：

```cpp
auto as_bool() const -> bool {
    if (auto* val = std::get_if<bool>(&value_)) {
        return *val;
    }
    throw type_error(type(), "boolean");  // 传递实际类型和请求类型
}
```

#### 动态错误消息构建
错误消息格式: `"Element is not a [requested] type (actual type: [actual])"`

**示例错误消息**:
- `"Element is not a string type (actual type: number)"`
- `"Element is not a boolean type (actual type: null)"`
- `"Element is not a number type (actual type: string)"`

#### 智能异常类型
```cpp
class type_error : public std::runtime_error {
private:
    ElementType actual_type_;
    std::string requested_type_;

public:
    type_error(ElementType actual_type, const std::string& requested_type);
    auto actual_type() const -> ElementType;
    auto requested_type() const -> const std::string&;
};
```

**特性**:
- 存储实际类型和请求类型信息
- 动态构建错误消息: `"Element is not a [requested] type (actual type: [actual])"`
- 提供类型信息的访问接口
- 支持程序化的错误处理

### 4. 零拷贝设计

#### 全面使用 string_view
- Element 中的 name_ 使用 `std::string_view`
- 字符串值使用 `std::string_view` 存储
- ObjectType 的 key 使用 `std::string_view`
- 避免不必要的字符串拷贝

```cpp
using ObjectType = std::unordered_map<std::string_view, Element>;
```

### 5. 可恢复解析架构

#### 解析状态管理
- `current_pos_` 记录当前解析位置
- `can_resume_` 标记是否可以恢复解析
- `resume_index_` 在 Element 中记录恢复位置

#### 恢复接口
```cpp
auto can_resume() const -> bool;
auto resume() -> void;
auto current_position() const -> size_t;
```

## 测试验证

### 1. 数据保护测试
```cpp
std::string json_data = "...";
rrjson::Parser parser(std::move(json_data));
// 验证 json_data 现在为空
assert(json_data.empty());
```

### 2. 类型错误测试
```cpp
rrjson::Element null_element;
try {
    null_element.as_bool();  // 应该抛出异常
} catch (const rrjson::type_error& e) {
    // 正确捕获类型错误
}
```

### 3. 详细类型错误测试
```cpp
rrjson::Element bool_element("test", true);

try {
    bool_element.as_string();  // 尝试将 bool 作为 string 访问
} catch (const rrjson::type_error& e) {
    // e.what() 返回: "Element is not a string type (actual type: boolean)"
    // e.actual_type() 返回: ElementType::Bool
    // e.requested_type() 返回: "string"

    std::cout << "Error: " << e.what() << std::endl;
    std::cout << "Actual: " << (int)e.actual_type() << std::endl;
    std::cout << "Requested: " << e.requested_type() << std::endl;
}
```

### 4. 移动语义测试
```cpp
// Parser 不能被拷贝
// rrjson::Parser parser2 = parser;  // 编译错误

// 但可以被移动
rrjson::Parser parser3 = std::move(parser);
```

## 架构优势

### 1. 内存安全
- 防止悬空指针：JSON 数据由 Parser 拥有
- 防止数据竞争：只读访问接口
- 防止意外修改：移动语义保护

### 2. 性能优化
- 零拷贝：全面使用 string_view
- 最小内存占用：避免不必要的字符串分配
- 高效访问：直接引用原始数据

### 3. 类型安全
- 编译时检查：强类型接口
- 运行时验证：类型不匹配时抛出异常
- 明确的错误信息：便于调试

### 4. 现代化设计
- C++17 特性：后置返回类型、移动语义
- RAII 原则：自动资源管理
- 异常安全：明确的错误处理

## 下一步实现

1. **词法分析器**: 实现 JSON token 解析
2. **语法分析器**: 实现递归下降解析
3. **恢复机制**: 实现中断和恢复功能
4. **性能优化**: 实现流式解析和内存池
5. **错误处理**: 提供详细的解析错误信息

## 使用示例

```cpp
#include "rrjson.hpp"
#include <iostream>
#include <string>

int main() {
    std::string json = R"({"name": "John", "age": 30})";
    
    try {
        rrjson::Parser parser(std::move(json));
        auto root = parser.parse();
        
        // 类型安全的访问
        std::cout << "Name: " << root["name"].as_string() << std::endl;
        std::cout << "Age: " << root["age"].as_int() << std::endl;
        
        // 错误示例：类型不匹配会抛出异常
        // std::cout << root["age"].as_string();  // 抛出 type_error
        
    } catch (const rrjson::type_error& e) {
        std::cerr << "Type error: " << e.what() << std::endl;
    }
    
    return 0;
}
```
