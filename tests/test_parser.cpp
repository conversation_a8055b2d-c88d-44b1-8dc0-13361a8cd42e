#include "rrjson.hpp"
#include <iostream>
#include <cassert>
#include <string>

void test_basic_parsing() {
    std::cout << "Testing basic JSON parsing...\n";

    std::string simple_json = R"({"key": "value"})";

    try {
        rrjson::Parser parser(std::move(simple_json));
        auto root = parser.parse();

        std::cout << "✓ Parser created successfully\n";
        std::cout << "✓ Data size: " << parser.data_size() << " bytes\n";

        // TODO: Implement actual tests when rrjson.hpp is complete
        std::cout << "✓ Basic parsing test placeholder\n";

    } catch (const std::exception& e) {
        std::cout << "✗ Error: " << e.what() << "\n";
    }
}

void test_array_parsing() {
    std::cout << "Testing array parsing...\n";

    std::string array_json = R"([1, 2, 3, "hello"])";

    try {
        rrjson::Parser parser(std::move(array_json));
        auto root = parser.parse();

        // TODO: Implement actual tests when rrjson.hpp is complete
        std::cout << "✓ Array parsing test placeholder\n";

    } catch (const std::exception& e) {
        std::cout << "✗ Error: " << e.what() << "\n";
    }
}

void test_nested_objects() {
    std::cout << "Testing nested objects...\n";

    std::string nested_json = R"({
        "user": {
            "name": "John",
            "details": {
                "age": 30,
                "city": "NYC"
            }
        }
    })";

    try {
        rrjson::Parser parser(std::move(nested_json));
        auto root = parser.parse();

        // TODO: Implement actual tests when rrjson.hpp is complete
        std::cout << "✓ Nested objects test placeholder\n";

    } catch (const std::exception& e) {
        std::cout << "✗ Error: " << e.what() << "\n";
    }
}

void test_type_error_handling() {
    std::cout << "Testing type error handling...\n";

    try {
        // Create a null element and try to access it as different types
        rrjson::Element null_element;

        // Test as_bool() with detailed error information
        try {
            null_element.as_bool();
            std::cout << "✗ Should have thrown type_error for as_bool()\n";
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Correctly threw type_error for as_bool(): " << e.what() << "\n";
            std::cout << "  - Actual type: " << (e.actual_type() == rrjson::ElementType::Null ? "Null" : "Other") << "\n";
            std::cout << "  - Requested type: " << e.requested_type() << "\n";
        }

        // Test as_string() with detailed error information
        try {
            null_element.as_string();
            std::cout << "✗ Should have thrown type_error for as_string()\n";
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Correctly threw type_error for as_string(): " << e.what() << "\n";
            std::cout << "  - Actual type: " << (e.actual_type() == rrjson::ElementType::Null ? "Null" : "Other") << "\n";
            std::cout << "  - Requested type: " << e.requested_type() << "\n";
        }

        // Test as_number() with detailed error information
        try {
            null_element.as_number();
            std::cout << "✗ Should have thrown type_error for as_number()\n";
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Correctly threw type_error for as_number(): " << e.what() << "\n";
            std::cout << "  - Actual type: " << (e.actual_type() == rrjson::ElementType::Null ? "Null" : "Other") << "\n";
            std::cout << "  - Requested type: " << e.requested_type() << "\n";
        }

    } catch (const std::exception& e) {
        std::cout << "✗ Unexpected error: " << e.what() << "\n";
    }
}

void test_different_element_types() {
    std::cout << "Testing different element types and their error messages...\n";

    try {
        // Create elements of different types
        rrjson::Element bool_element("test", true);
        rrjson::Element number_element("test", 42.0);
        rrjson::Element string_element("test", std::string_view("hello"));

        // Test bool element accessed as wrong types
        try {
            bool_element.as_string();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Bool->String error: " << e.what() << "\n";
        }

        try {
            bool_element.as_number();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Bool->Number error: " << e.what() << "\n";
        }

        // Test number element accessed as wrong types
        try {
            number_element.as_bool();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Number->Bool error: " << e.what() << "\n";
        }

        try {
            number_element.as_string();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ Number->String error: " << e.what() << "\n";
        }

        // Test string element accessed as wrong types
        try {
            string_element.as_bool();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ String->Bool error: " << e.what() << "\n";
        }

        try {
            string_element.as_number();
        } catch (const rrjson::type_error& e) {
            std::cout << "✓ String->Number error: " << e.what() << "\n";
        }

        // Test correct access (should not throw)
        std::cout << "✓ Correct access - Bool: " << (bool_element.as_bool() ? "true" : "false") << "\n";
        std::cout << "✓ Correct access - Number: " << number_element.as_number() << "\n";
        std::cout << "✓ Correct access - String: " << string_element.as_string() << "\n";

    } catch (const std::exception& e) {
        std::cout << "✗ Unexpected error: " << e.what() << "\n";
    }
}

void test_resumability() {
    std::cout << "Testing resumability feature...\n";

    std::string large_json = R"({"data": [1, 2, 3, 4, 5]})";

    try {
        rrjson::Parser parser(std::move(large_json));

        std::cout << "✓ Initial can_resume: " << (parser.can_resume() ? "yes" : "no") << "\n";

        auto root = parser.parse();

        std::cout << "✓ After parse can_resume: " << (parser.can_resume() ? "yes" : "no") << "\n";
        std::cout << "✓ Current position: " << parser.current_position() << "\n";

        // TODO: Implement resumability tests
        std::cout << "✓ Resumability test placeholder\n";

    } catch (const std::exception& e) {
        std::cout << "✗ Error: " << e.what() << "\n";
    }
}

int main() {
    std::cout << "rrjson Test Suite\n";
    std::cout << "=================\n\n";

    test_basic_parsing();
    std::cout << "\n";

    test_array_parsing();
    std::cout << "\n";

    test_nested_objects();
    std::cout << "\n";

    test_type_error_handling();
    std::cout << "\n";

    test_different_element_types();
    std::cout << "\n";

    test_resumability();
    std::cout << "\n";

    std::cout << "All tests completed!\n";
    std::cout << "Note: Actual JSON parsing implementation needed in rrjson.hpp\n";
    std::cout << "Current implementation demonstrates:\n";
    std::cout << "- Move semantics for JSON data protection\n";
    std::cout << "- Trailing return types\n";
    std::cout << "- Type-safe as_xxx() methods with detailed error messages\n";
    std::cout << "- Dynamic error message construction with actual and requested types\n";
    std::cout << "- string_view usage throughout Element class\n";

    return 0;
}
