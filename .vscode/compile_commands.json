[{"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-Wall", "-Werror", "-Ibuild/.gens/example/linux/x86_64/platform/windows/idl", "-Il<PERSON>", "-o", "build/.objs/example/linux/x86_64/examples/basic_usage.cpp.o", "examples/basic_usage.cpp"], "file": "examples/basic_usage.cpp"}, {"directory": "/home/<USER>/Desktop/rrjson", "arguments": ["/usr/bin/gcc", "-c", "-m64", "-target", "x86_64-linux-gnu", "-Wall", "-Werror", "-Ibuild/.gens/test/linux/x86_64/platform/windows/idl", "-Il<PERSON>", "-o", "build/.objs/test/linux/x86_64/tests/test_parser.cpp.o", "tests/test_parser.cpp"], "file": "tests/test_parser.cpp"}]