#pragma once

#include <string_view>
#include <span>
#include <memory>
#include <vector>
#include <unordered_map>
#include <variant>
#include <optional>
#include <stdexcept>
#include <string>

namespace rrjson {

/**
 * @brief JSON element types
 */
enum class ElementType {
    Null,
    Bool,
    Number,
    String,
    Array,
    Object
};

// Forward declarations
class Element;
class Parser;
class JsonDataHelper;

/**
 * @brief Exception thrown when type conversion fails
 */
class type_error : public std::runtime_error {
private:
    ElementType actual_type_;
    std::string requested_type_;

    static auto element_type_to_string(ElementType type) -> std::string {
        switch (type) {
            case ElementType::Null:   return "null";
            case ElementType::Bool:   return "boolean";
            case ElementType::Number: return "number";
            case ElementType::String: return "string";
            case ElementType::Array:  return "array";
            case ElementType::Object: return "object";
            default: return "unknown";
        }
    }

    static auto build_error_message(ElementType actual, const std::string& requested) -> std::string {
        return "Element is not a " + requested + " type (actual type: " + element_type_to_string(actual) + ")";
    }

public:
    type_error(ElementType actual_type, const std::string& requested_type)
        : std::runtime_error(build_error_message(actual_type, requested_type))
        , actual_type_(actual_type)
        , requested_type_(requested_type) {}

    auto actual_type() const -> ElementType { return actual_type_; }
    auto requested_type() const -> const std::string& { return requested_type_; }
};

/**
 * @brief Helper class to manage JSON raw data and prevent user modification
 */
class JsonDataHelper {
private:
    std::string json_data_;  // Owned copy of JSON data

public:
    // Move constructor to take ownership of JSON data
    explicit JsonDataHelper(std::string&& json_raw)
        : json_data_(std::move(json_raw)) {}

    // Delete copy operations to prevent copying
    JsonDataHelper(const JsonDataHelper&) = delete;
    auto operator=(const JsonDataHelper&) -> JsonDataHelper& = delete;

    // Allow move operations
    JsonDataHelper(JsonDataHelper&&) = default;
    auto operator=(JsonDataHelper&&) -> JsonDataHelper& = default;

    // Get read-only view of the data
    auto data() const -> std::string_view {
        return json_data_;
    }

    auto size() const -> size_t {
        return json_data_.size();
    }

    auto empty() const -> bool {
        return json_data_.empty();
    }
};

/**
 * @brief Represents a JSON element with resumable parsing capability
 */
class Element {
public:
    using ArrayType = std::vector<Element>;
    using ObjectType = std::unordered_map<std::string_view, Element>;
    using ValueType = std::variant<
        std::nullptr_t,     // Null
        bool,               // Bool
        double,             // Number
        std::string_view,   // String
        ArrayType,          // Array
        ObjectType          // Object
    >;

private:
    std::string_view name_;
    ValueType value_;
    size_t resume_index_;

public:
    Element() : name_(), value_(nullptr), resume_index_(0) {}

    explicit Element(std::string_view name, ValueType value, size_t resume_idx = 0)
        : name_(name), value_(std::move(value)), resume_index_(resume_idx) {}

    // Type checking with trailing return type
    auto type() const -> ElementType {
        return static_cast<ElementType>(value_.index());
    }

    auto is_null() const -> bool { return type() == ElementType::Null; }
    auto is_bool() const -> bool { return type() == ElementType::Bool; }
    auto is_number() const -> bool { return type() == ElementType::Number; }
    auto is_string() const -> bool { return type() == ElementType::String; }
    auto is_array() const -> bool { return type() == ElementType::Array; }
    auto is_object() const -> bool { return type() == ElementType::Object; }

    // Value accessors with trailing return type and error throwing
    auto as_bool() const -> bool {
        if (auto* val = std::get_if<bool>(&value_)) {
            return *val;
        }
        throw type_error(type(), "boolean");
    }

    auto as_number() const -> double {
        if (auto* val = std::get_if<double>(&value_)) {
            return *val;
        }
        throw type_error(type(), "number");
    }

    auto as_int() const -> int {
        return static_cast<int>(as_number());
    }

    auto as_string() const -> std::string_view {
        if (auto* val = std::get_if<std::string_view>(&value_)) {
            return *val;
        }
        throw type_error(type(), "string");
    }

    // Container accessors with trailing return type
    auto operator[](std::string_view key) const -> Element {
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            auto it = obj->find(key);
            if (it != obj->end()) {
                return it->second;
            }
        }
        return Element(); // Return null element if not found
    }

    auto operator[](size_t index) const -> Element {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            if (index < arr->size()) {
                return (*arr)[index];
            }
        }
        return Element(); // Return null element if out of bounds
    }

    auto size() const -> size_t {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            return arr->size();
        }
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            return obj->size();
        }
        return 0;
    }

    // Resume functionality with trailing return type
    auto resume_index() const -> size_t { return resume_index_; }
    auto set_resume_index(size_t idx) -> void { resume_index_ = idx; }

    auto name() const -> std::string_view { return name_; }

    // Explicit conversion to bool for validity checking
    explicit operator bool() const {
        return !is_null();
    }
};

/**
 * @brief Resumable JSON parser
 */
class Parser {
private:
    std::unique_ptr<JsonDataHelper> json_helper_;
    std::string_view json_view_;
    size_t current_pos_;
    bool can_resume_;

public:
    // Constructor that takes ownership of JSON data
    explicit Parser(std::string&& json_raw)
        : json_helper_(std::make_unique<JsonDataHelper>(std::move(json_raw)))
        , json_view_(json_helper_->data())
        , current_pos_(0)
        , can_resume_(false) {}

    // Delete copy operations to prevent copying
    Parser(const Parser&) = delete;
    auto operator=(const Parser&) -> Parser& = delete;

    // Allow move operations
    Parser(Parser&&) = default;
    auto operator=(Parser&&) -> Parser& = default;

    /**
     * @brief Parse JSON text into Element tree
     * @return Root element of the parsed JSON
     */
    auto parse() -> Element {
        // TODO: Implement actual JSON parsing
        // This is a placeholder implementation
        current_pos_ = 0;
        can_resume_ = true;

        // For now, return a null element
        return Element();
    }

    /**
     * @brief Check if parsing can be resumed
     */
    auto can_resume() const -> bool {
        return can_resume_;
    }

    /**
     * @brief Resume parsing from the last position
     */
    auto resume() -> void {
        if (can_resume_) {
            // TODO: Implement resume functionality
        }
    }

    /**
     * @brief Get current parsing position
     */
    auto current_position() const -> size_t {
        return current_pos_;
    }

    /**
     * @brief Get the size of JSON data
     */
    auto data_size() const -> size_t {
        return json_helper_ ? json_helper_->size() : 0;
    }

private:
    // TODO: Implement lexer and parsing methods with trailing return types
    // - auto skip_whitespace() -> void;
    // - auto parse_value() -> Element;
    // - auto parse_object() -> Element;
    // - auto parse_array() -> Element;
    // - auto parse_string() -> std::string_view;
    // - auto parse_number() -> double;
    // - auto parse_literal() -> Element;
};

} // namespace rrjson
