#pragma once

#include <string_view>
#include <span>
#include <memory>
#include <vector>
#include <unordered_map>
#include <variant>
#include <optional>

namespace rrjson {

// Forward declarations
class Element;
class Parser;

/**
 * @brief JSON element types
 */
enum class ElementType {
    Null,
    Bool,
    Number,
    String,
    Array,
    Object
};

/**
 * @brief Represents a JSON element with resumable parsing capability
 */
class Element {
public:
    using ArrayType = std::vector<Element>;
    using ObjectType = std::unordered_map<std::string, Element>;
    using ValueType = std::variant<
        std::nullptr_t,     // Null
        bool,               // Bool
        double,             // Number
        std::string_view,   // String
        ArrayType,          // Array
        ObjectType          // Object
    >;

private:
    std::string_view name_;
    ValueType value_;
    size_t resume_index_;

public:
    Element() : name_(), value_(nullptr), resume_index_(0) {}

    explicit Element(std::string_view name, ValueType value, size_t resume_idx = 0)
        : name_(name), value_(std::move(value)), resume_index_(resume_idx) {}

    // Type checking
    ElementType type() const {
        return static_cast<ElementType>(value_.index());
    }

    bool is_null() const { return type() == ElementType::Null; }
    bool is_bool() const { return type() == ElementType::Bool; }
    bool is_number() const { return type() == ElementType::Number; }
    bool is_string() const { return type() == ElementType::String; }
    bool is_array() const { return type() == ElementType::Array; }
    bool is_object() const { return type() == ElementType::Object; }

    // Value accessors
    bool as_bool() const {
        if (auto* val = std::get_if<bool>(&value_)) {
            return *val;
        }
        return false;
    }

    double as_number() const {
        if (auto* val = std::get_if<double>(&value_)) {
            return *val;
        }
        return 0.0;
    }

    int as_int() const {
        return static_cast<int>(as_number());
    }

    std::string_view as_string() const {
        if (auto* val = std::get_if<std::string_view>(&value_)) {
            return *val;
        }
        return {};
    }

    // Container accessors
    Element operator[](std::string_view key) const {
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            auto it = obj->find(std::string(key));
            if (it != obj->end()) {
                return it->second;
            }
        }
        return Element(); // Return null element if not found
    }

    Element operator[](size_t index) const {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            if (index < arr->size()) {
                return (*arr)[index];
            }
        }
        return Element(); // Return null element if out of bounds
    }

    size_t size() const {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            return arr->size();
        }
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            return obj->size();
        }
        return 0;
    }

    // Resume functionality
    size_t resume_index() const { return resume_index_; }
    void set_resume_index(size_t idx) { resume_index_ = idx; }

    std::string_view name() const { return name_; }

    // Explicit conversion to bool for validity checking
    explicit operator bool() const {
        return !is_null();
    }
};

/**
 * @brief Resumable JSON parser
 */
class Parser {
private:
    std::string_view json_text_;
    size_t current_pos_;
    bool can_resume_;

public:
    explicit Parser(std::string_view json_text)
        : json_text_(json_text), current_pos_(0), can_resume_(false) {}

    /**
     * @brief Parse JSON text into Element tree
     * @return Root element of the parsed JSON
     */
    Element parse() {
        // TODO: Implement actual JSON parsing
        // This is a placeholder implementation
        current_pos_ = 0;
        can_resume_ = true;

        // For now, return a null element
        return Element();
    }

    /**
     * @brief Check if parsing can be resumed
     */
    bool can_resume() const {
        return can_resume_;
    }

    /**
     * @brief Resume parsing from the last position
     */
    void resume() {
        if (can_resume_) {
            // TODO: Implement resume functionality
        }
    }

    /**
     * @brief Get current parsing position
     */
    size_t current_position() const {
        return current_pos_;
    }

private:
    // TODO: Implement lexer and parsing methods
    // - skip_whitespace()
    // - parse_value()
    // - parse_object()
    // - parse_array()
    // - parse_string()
    // - parse_number()
    // - parse_literal()
};

} // namespace rrjson
