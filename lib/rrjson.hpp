#pragma once

#include <string_view>
#include <span>
#include <memory>
#include <vector>
#include <unordered_map>
#include <variant>
#include <optional>
#include <stdexcept>
#include <string>

namespace rrjson {

/**
 * @brief JSON element types
 */
enum class ElementType {
    Null,
    Bool,
    Number,
    String,
    Array,
    Object
};

// Forward declarations
class Element;
class Parser;
class JsonDataHelper;

/**
 * @brief Exception thrown when type conversion fails
 */
class type_error : public std::runtime_error {
private:
    ElementType actual_type_;
    std::string requested_type_;

    static auto build_error_message(ElementType actual, const std::string& requested) -> std::string {
        return "Element is not a " + requested + " type (actual type: " + element_type_to_string(actual) + ")";
    }

public:
    static auto element_type_to_string(ElementType type) -> std::string {
        switch (type) {
            case ElementType::Null:   return "null";
            case ElementType::Bool:   return "boolean";
            case ElementType::Number: return "number";
            case ElementType::String: return "string";
            case ElementType::Array:  return "array";
            case ElementType::Object: return "object";
            default: return "unknown";
        }
    }

    type_error(ElementType actual_type, const std::string& requested_type)
        : std::runtime_error(build_error_message(actual_type, requested_type))
        , actual_type_(actual_type)
        , requested_type_(requested_type) {}

    auto actual_type() const -> ElementType { return actual_type_; }
    auto requested_type() const -> const std::string& { return requested_type_; }
};

/**
 * @brief Exception thrown when index access fails
 */
class index_error : public std::runtime_error {
private:
    size_t index_;
    size_t size_;
    ElementType actual_type_;

public:
    index_error(size_t index, size_t size, ElementType actual_type)
        : std::runtime_error("Index " + std::to_string(index) + " is out of bounds (size: " +
                           std::to_string(size) + ", element type: " +
                           type_error::element_type_to_string(actual_type) + ")")
        , index_(index)
        , size_(size)
        , actual_type_(actual_type) {}

    auto index() const -> size_t { return index_; }
    auto size() const -> size_t { return size_; }
    auto actual_type() const -> ElementType { return actual_type_; }
};

/**
 * @brief Exception thrown when key access fails
 */
class key_error : public std::runtime_error {
private:
    std::string key_;
    ElementType actual_type_;

public:
    key_error(std::string_view key, ElementType actual_type)
        : std::runtime_error("Key '" + std::string(key) + "' not found in " +
                           type_error::element_type_to_string(actual_type) + " element")
        , key_(key)
        , actual_type_(actual_type) {}

    auto key() const -> const std::string& { return key_; }
    auto actual_type() const -> ElementType { return actual_type_; }
};

/**
 * @brief Exception thrown when JSON parsing fails
 */
class parse_error : public std::runtime_error {
private:
    size_t position_;
    char unexpected_char_;

public:
    parse_error(const std::string& message, size_t position, char unexpected_char = '\0')
        : std::runtime_error(message + " at position " + std::to_string(position) +
                           (unexpected_char != '\0' ? " (unexpected character: '" + std::string(1, unexpected_char) + "')" : ""))
        , position_(position)
        , unexpected_char_(unexpected_char) {}

    auto position() const -> size_t { return position_; }
    auto unexpected_char() const -> char { return unexpected_char_; }
};



/**
 * @brief Helper class to manage JSON raw data and prevent user modification
 */
class JsonDataHelper {
private:
    std::string json_data_;  // Owned copy of JSON data

public:
    // Move constructor to take ownership of JSON data
    explicit JsonDataHelper(std::string&& json_raw)
        : json_data_(std::move(json_raw)) {}

    // Delete copy operations to prevent copying
    JsonDataHelper(const JsonDataHelper&) = delete;
    auto operator=(const JsonDataHelper&) -> JsonDataHelper& = delete;

    // Allow move operations
    JsonDataHelper(JsonDataHelper&&) = default;
    auto operator=(JsonDataHelper&&) -> JsonDataHelper& = default;

    // Get read-only view of the data
    auto data() const -> std::string_view {
        return json_data_;
    }

    auto size() const -> size_t {
        return json_data_.size();
    }

    auto empty() const -> bool {
        return json_data_.empty();
    }
};

// Forward declaration for Parser
class Parser;

/**
 * @brief Represents a JSON element with resumable parsing capability
 */
class Element {
    friend class Parser;  // Allow Parser to access private members

public:
    using ArrayType = std::vector<Element>;
    using ObjectType = std::unordered_map<std::string_view, Element>;
    using ValueType = std::variant<
        std::nullptr_t,     // Null
        bool,               // Bool
        double,             // Number
        std::string_view,   // String
        ArrayType,          // Array
        ObjectType          // Object
    >;

private:
    std::string_view name_;
    ValueType value_;
    size_t resume_index_;
    std::shared_ptr<Parser> parser_;  // Shared parser for resumable parsing

public:
    Element() : name_(), value_(nullptr), resume_index_(0) {}

    explicit Element(std::string_view name, ValueType value, size_t resume_idx = 0, std::shared_ptr<Parser> parser = nullptr)
        : name_(name), value_(std::move(value)), resume_index_(resume_idx), parser_(parser) {}

    // Type checking with trailing return type
    auto type() const -> ElementType {
        return static_cast<ElementType>(value_.index());
    }

    auto is_null() const -> bool { return type() == ElementType::Null; }
    auto is_bool() const -> bool { return type() == ElementType::Bool; }
    auto is_number() const -> bool { return type() == ElementType::Number; }
    auto is_string() const -> bool { return type() == ElementType::String; }
    auto is_array() const -> bool { return type() == ElementType::Array; }
    auto is_object() const -> bool { return type() == ElementType::Object; }

    // Value accessors with trailing return type and error throwing
    auto as_bool() const -> bool {
        if (auto* val = std::get_if<bool>(&value_)) {
            return *val;
        }
        throw type_error(type(), "boolean");
    }

    auto as_number() const -> double {
        if (auto* val = std::get_if<double>(&value_)) {
            return *val;
        }
        throw type_error(type(), "number");
    }

    auto as_int() const -> int {
        return static_cast<int>(as_number());
    }

    auto as_string() const -> std::string_view {
        if (auto* val = std::get_if<std::string_view>(&value_)) {
            return *val;
        }
        throw type_error(type(), "string");
    }

    // Container accessors with trailing return type - support resumable parsing
    auto operator[](std::string_view key) const -> Element {
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            auto it = obj->find(key);
            if (it != obj->end()) {
                return it->second;
            }

            // Key not found - try to resume parsing if possible
            if (parser_ && can_resume_parsing()) {
                return try_resume_and_find_key(key);
            }

            // Key not found and cannot resume
            throw key_error(key, type());
        }
        // Not an object type
        throw type_error(type(), "object");
    }

    auto operator[](size_t index) const -> Element {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            if (index < arr->size()) {
                return (*arr)[index];
            }

            // Index out of bounds - try to resume parsing if possible
            if (parser_ && can_resume_parsing()) {
                return try_resume_and_find_index(index);
            }

            // Index out of bounds and cannot resume
            throw index_error(index, arr->size(), type());
        }
        // Not an array type
        throw type_error(type(), "array");
    }

    auto size() const -> size_t {
        if (auto* arr = std::get_if<ArrayType>(&value_)) {
            return arr->size();
        }
        if (auto* obj = std::get_if<ObjectType>(&value_)) {
            return obj->size();
        }
        // Not a container type
        throw type_error(type(), "array or object");
    }

    // Resume functionality with trailing return type
    auto resume_index() const -> size_t { return resume_index_; }
    auto set_resume_index(size_t idx) -> void { resume_index_ = idx; }

    auto name() const -> std::string_view { return name_; }

    // Explicit conversion to bool for validity checking
    explicit operator bool() const {
        return !is_null();
    }

private:
    // Resumable parsing helper methods
    auto can_resume_parsing() const -> bool {
        return parser_ && resume_index_ > 0;
    }

    auto try_resume_and_find_key(std::string_view key) const -> Element {
        if (!parser_) {
            throw key_error(key, type());
        }

        // Try to resume parsing from the stored position
        auto* obj = std::get_if<ObjectType>(&value_);
        if (!obj) {
            throw key_error(key, type());
        }

        // Create a mutable copy for resuming
        auto mutable_this = const_cast<Element*>(this);
        if (mutable_this->resume_object_parsing()) {
            // Check again after resuming
            auto it = obj->find(key);
            if (it != obj->end()) {
                return it->second;
            }
        }

        // Still not found after resuming
        throw key_error(key, type());
    }

    auto try_resume_and_find_index(size_t index) const -> Element {
        if (!parser_) {
            throw index_error(index, 0, type());
        }

        // Try to resume parsing from the stored position
        auto* arr = std::get_if<ArrayType>(&value_);
        if (!arr) {
            throw index_error(index, 0, type());
        }

        // Create a mutable copy for resuming
        auto mutable_this = const_cast<Element*>(this);
        if (mutable_this->resume_array_parsing()) {
            // Check again after resuming
            if (index < arr->size()) {
                return (*arr)[index];
            }
        }

        // Still out of bounds after resuming
        throw index_error(index, arr->size(), type());
    }

    auto resume_object_parsing() -> bool;  // Defined after Parser class
    auto resume_array_parsing() -> bool;   // Defined after Parser class
};

/**
 * @brief Resumable JSON parser - shared among Elements for resumable parsing
 */
class Parser {
private:
    std::unique_ptr<JsonDataHelper> json_helper_;
    std::string_view json_view_;
    mutable size_t current_pos_;  // Mutable for const methods
    mutable bool can_resume_;     // Mutable for const methods

public:
    // Constructor that takes ownership of JSON data
    explicit Parser(std::string&& json_raw)
        : json_helper_(std::make_unique<JsonDataHelper>(std::move(json_raw)))
        , json_view_(json_helper_->data())
        , current_pos_(0)
        , can_resume_(false) {}

    // Delete copy operations to prevent copying
    Parser(const Parser&) = delete;
    auto operator=(const Parser&) -> Parser& = delete;

    // Allow move operations
    Parser(Parser&&) = default;
    auto operator=(Parser&&) -> Parser& = default;

    /**
     * @brief Parse JSON text into Element tree
     * @return Root element of the parsed JSON
     */
    auto parse() -> Element {
        current_pos_ = 0;
        can_resume_ = true;

        skip_whitespace();
        if (current_pos_ >= json_view_.size()) {
            throw parse_error("Empty JSON input", current_pos_);
        }

        // Create shared_ptr to this parser for Elements to use
        auto shared_this = std::shared_ptr<Parser>(this, [](Parser*) {
            // Custom deleter that does nothing - the Parser is managed externally
        });

        auto result = parse_value(shared_this);
        skip_whitespace();

        // For root element, we can finish parsing completely
        // But for nested objects/arrays, we might want to keep the parser for resuming
        if (current_pos_ < json_view_.size()) {
            // Don't throw error immediately - allow resumable parsing
            can_resume_ = true;
        } else {
            can_resume_ = false;
        }

        return result;
    }

    /**
     * @brief Check if parsing can be resumed
     */
    auto can_resume() const -> bool {
        return can_resume_;
    }

    /**
     * @brief Resume parsing from the last position
     */
    auto resume() -> void {
        if (can_resume_) {
            // TODO: Implement resume functionality
        }
    }

    /**
     * @brief Get current parsing position
     */
    auto current_position() const -> size_t {
        return current_pos_;
    }

    /**
     * @brief Get the size of JSON data
     */
    auto data_size() const -> size_t {
        return json_helper_ ? json_helper_->size() : 0;
    }

    /**
     * @brief Resume parsing for object from given position
     */
    auto resume_object_parsing(Element& element) const -> bool {
        if (!can_resume_ || element.resume_index() == 0) {
            return false;
        }

        auto saved_pos = current_pos_;
        current_pos_ = element.resume_index();

        try {
            auto* obj = std::get_if<Element::ObjectType>(&element.value_);
            if (!obj) return false;

            // Continue parsing object from resume position
            // Create a temporary shared_ptr for the method call
            auto temp_parser = element.parser_;
            return continue_object_parsing(*obj, temp_parser);
        } catch (...) {
            current_pos_ = saved_pos;
            return false;
        }
    }

    /**
     * @brief Resume parsing for array from given position
     */
    auto resume_array_parsing(Element& element) const -> bool {
        if (!can_resume_ || element.resume_index() == 0) {
            return false;
        }

        auto saved_pos = current_pos_;
        current_pos_ = element.resume_index();

        try {
            auto* arr = std::get_if<Element::ArrayType>(&element.value_);
            if (!arr) return false;

            // Continue parsing array from resume position
            return continue_array_parsing(*arr, std::shared_ptr<Parser>(this, [](Parser*){}));
        } catch (...) {
            current_pos_ = saved_pos;
            return false;
        }
    }

private:
    // Lexer and parsing methods with trailing return types
    auto skip_whitespace() const -> void {
        while (current_pos_ < json_view_.size() &&
               std::isspace(static_cast<unsigned char>(json_view_[current_pos_]))) {
            ++current_pos_;
        }
    }

    auto peek_char() const -> char {
        return current_pos_ < json_view_.size() ? json_view_[current_pos_] : '\0';
    }

    auto consume_char() const -> char {
        if (current_pos_ >= json_view_.size()) {
            throw parse_error("Unexpected end of input", current_pos_);
        }
        return json_view_[current_pos_++];
    }

    auto expect_char(char expected) const -> void {
        auto ch = consume_char();
        if (ch != expected) {
            throw parse_error("Expected '" + std::string(1, expected) + "'", current_pos_ - 1, ch);
        }
    }

    auto parse_value(std::shared_ptr<Parser> parser_ref) const -> Element {
        skip_whitespace();

        if (current_pos_ >= json_view_.size()) {
            throw parse_error("Unexpected end of input while parsing value", current_pos_);
        }

        switch (peek_char()) {
            case '"':  return parse_string_element();
            case '{':  return parse_object(parser_ref);
            case '[':  return parse_array(parser_ref);
            case 't':
            case 'f':
            case 'n':  return parse_literal();
            case '-':
            case '0': case '1': case '2': case '3': case '4':
            case '5': case '6': case '7': case '8': case '9':
                return parse_number_element();
            default:
                throw parse_error("Invalid JSON value", current_pos_, peek_char());
        }
    }

    auto parse_string_element() const -> Element {
        expect_char('"');
        auto start = current_pos_;

        while (current_pos_ < json_view_.size() && json_view_[current_pos_] != '"') {
            if (json_view_[current_pos_] == '\\') {
                ++current_pos_; // Skip escape character
                if (current_pos_ >= json_view_.size()) {
                    throw parse_error("Unterminated string escape", current_pos_);
                }
            }
            ++current_pos_;
        }

        if (current_pos_ >= json_view_.size()) {
            throw parse_error("Unterminated string", current_pos_);
        }

        auto string_view = json_view_.substr(start, current_pos_ - start);
        expect_char('"');

        return Element("", string_view);
    }

    auto parse_number_element() const -> Element {
        auto start = current_pos_;

        // Handle negative sign
        if (peek_char() == '-') {
            ++current_pos_;
        }

        // Parse integer part
        if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
            throw parse_error("Invalid number", current_pos_, peek_char());
        }

        if (peek_char() == '0') {
            ++current_pos_;
        } else {
            while (current_pos_ < json_view_.size() &&
                   std::isdigit(static_cast<unsigned char>(peek_char()))) {
                ++current_pos_;
            }
        }

        // Parse decimal part
        if (peek_char() == '.') {
            ++current_pos_;
            if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
                throw parse_error("Invalid number: missing digits after decimal", current_pos_);
            }
            while (current_pos_ < json_view_.size() &&
                   std::isdigit(static_cast<unsigned char>(peek_char()))) {
                ++current_pos_;
            }
        }

        // Parse exponent part
        if (peek_char() == 'e' || peek_char() == 'E') {
            ++current_pos_;
            if (peek_char() == '+' || peek_char() == '-') {
                ++current_pos_;
            }
            if (!std::isdigit(static_cast<unsigned char>(peek_char()))) {
                throw parse_error("Invalid number: missing digits in exponent", current_pos_);
            }
            while (current_pos_ < json_view_.size() &&
                   std::isdigit(static_cast<unsigned char>(peek_char()))) {
                ++current_pos_;
            }
        }

        auto number_str = json_view_.substr(start, current_pos_ - start);
        auto number = std::stod(std::string(number_str));

        return Element("", number);
    }

    auto parse_literal() const -> Element {
        if (current_pos_ + 4 <= json_view_.size() &&
            json_view_.substr(current_pos_, 4) == "true") {
            current_pos_ += 4;
            return Element("", true);
        }

        if (current_pos_ + 5 <= json_view_.size() &&
            json_view_.substr(current_pos_, 5) == "false") {
            current_pos_ += 5;
            return Element("", false);
        }

        if (current_pos_ + 4 <= json_view_.size() &&
            json_view_.substr(current_pos_, 4) == "null") {
            current_pos_ += 4;
            return Element("", nullptr);
        }

        throw parse_error("Invalid literal", current_pos_, peek_char());
    }

    auto parse_object(std::shared_ptr<Parser> parser_ref) const -> Element {
        expect_char('{');
        skip_whitespace();

        Element::ObjectType obj;
        auto resume_pos = current_pos_;

        if (peek_char() == '}') {
            ++current_pos_;
            return Element("", std::move(obj), 0, parser_ref);
        }

        while (true) {
            skip_whitespace();

            // Parse key
            if (peek_char() != '"') {
                throw parse_error("Expected string key in object", current_pos_, peek_char());
            }

            auto key_element = parse_string_element();
            auto key = key_element.as_string();

            skip_whitespace();
            expect_char(':');
            skip_whitespace();

            // Parse value
            auto value = parse_value(parser_ref);
            obj[key] = std::move(value);

            skip_whitespace();

            if (peek_char() == '}') {
                ++current_pos_;
                break;
            }

            if (peek_char() == ',') {
                ++current_pos_;
                continue;
            }

            throw parse_error("Expected ',' or '}' in object", current_pos_, peek_char());
        }

        return Element("", std::move(obj), resume_pos, parser_ref);
    }

    auto parse_array(std::shared_ptr<Parser> parser_ref) const -> Element {
        expect_char('[');
        skip_whitespace();

        Element::ArrayType arr;
        auto resume_pos = current_pos_;

        if (peek_char() == ']') {
            ++current_pos_;
            return Element("", std::move(arr), 0, parser_ref);
        }

        while (true) {
            skip_whitespace();

            auto value = parse_value(parser_ref);
            arr.push_back(std::move(value));

            skip_whitespace();

            if (peek_char() == ']') {
                ++current_pos_;
                break;
            }

            if (peek_char() == ',') {
                ++current_pos_;
                continue;
            }

            throw parse_error("Expected ',' or ']' in array", current_pos_, peek_char());
        }

        return Element("", std::move(arr), resume_pos, parser_ref);
    }

    auto continue_object_parsing(Element::ObjectType& obj, std::shared_ptr<Parser> parser_ref) const -> bool {
        // Implementation for continuing object parsing from resume point
        // This is a simplified version - in practice, you'd need to handle partial parsing state
        try {
            skip_whitespace();

            while (current_pos_ < json_view_.size() && peek_char() != '}') {
                if (peek_char() == ',') {
                    ++current_pos_;
                    skip_whitespace();
                }

                if (peek_char() != '"') break;

                auto key_element = parse_string_element();
                auto key = key_element.as_string();

                skip_whitespace();
                expect_char(':');
                skip_whitespace();

                auto value = parse_value(parser_ref);
                obj[key] = std::move(value);

                skip_whitespace();
            }

            return true;
        } catch (...) {
            return false;
        }
    }

    auto continue_array_parsing(Element::ArrayType& arr, std::shared_ptr<Parser> parser_ref) const -> bool {
        // Implementation for continuing array parsing from resume point
        try {
            skip_whitespace();

            while (current_pos_ < json_view_.size() && peek_char() != ']') {
                if (peek_char() == ',') {
                    ++current_pos_;
                    skip_whitespace();
                }

                auto value = parse_value(parser_ref);
                arr.push_back(std::move(value));

                skip_whitespace();
            }

            return true;
        } catch (...) {
            return false;
        }
    }
};

// Element method implementations that depend on Parser
inline auto Element::resume_object_parsing() -> bool {
    if (!parser_) return false;
    return parser_->resume_object_parsing(*this);
}

inline auto Element::resume_array_parsing() -> bool {
    if (!parser_) return false;
    return parser_->resume_array_parsing(*this);
}

} // namespace rrjson
